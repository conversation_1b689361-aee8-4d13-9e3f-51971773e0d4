# -*- coding: gbk -*-
"""
ZZsignal.py - 技术信号检测与分析程序
基于 Zstock_tech_qmttdx.py 中的技术信号计算逻辑，提供标准化的技术信号检测功能

主要功能：
1. 检测基于K线数据、成交量和MACD等经典指标的技术信号
2. 为每个技术信号建立标准化的数据结构
3. 提供可被其他程序调用的接口，返回结构化的信号数据
4. 支持用户交互界面，输出清晰可读的信号分析

技术信号类型：
- MACD信号：DIF拐头向上/向下、金叉/死叉、红柱变长/缩短等
- 移动平均线信号：MA5拐头向上、MA5上穿/下穿MA10、股价运行在MA5之上等
- RSI信号：RSI6上穿/下穿50、RSI6始终运行在50以上等
- 成交量信号：放量、MAVOL5上涨/下跌、VOL大于/小于MAVOL5等

作者：基于 Zstock_tech_qmttdx.py 重构
日期：2025-01-28
"""

import pandas as pd
import numpy as np
import sys
import os
import re
from datetime import datetime, timedelta
import time
import json


def clean_timestamp_str(timestamp_str):
    """清理时间戳字符串，去除.000000000后缀"""
    if isinstance(timestamp_str, str):
        return timestamp_str.replace('.000000000', '')
    return str(timestamp_str).replace('.000000000', '')

# 添加项目目录到路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入数据获取和指标计算模块
try:
    from ZZfetch import get_fetcher, connect_miniqmt, disconnect_miniqmt
    ZZFETCH_AVAILABLE = True
except ImportError:
    ZZFETCH_AVAILABLE = False
    print("错误: ZZfetch模块未找到，无法自动获取K线数据")

try:
    from ZZindicator import TechnicalIndicators, calculate_indicators_from_dataframe
    ZZINDICATOR_AVAILABLE = True
except ImportError:
    ZZINDICATOR_AVAILABLE = False
    print("错误: ZZindicator模块未找到，无法计算技术指标")


class TechnicalSignal:
    """技术信号数据结构"""
    
    def __init__(self, signal_name, timeframe, signal_time, signal_values,
                 signal_strength=None, signal_direction=None, description=None):
        """
        初始化技术信号

        参数:
            signal_name: 信号名称（如"DIF拐头向上"）
            timeframe: 所属时间周期（如"日线"、"30分钟线"）
            signal_time: 信号发生时间或起止时间
            signal_values: 相关指标的具体数值表现（字典格式，支持时间序列数据）
            signal_strength: 信号强度或置信度（可选）
            signal_direction: 信号方向（"看涨"、"看跌"、"中性"）
            description: 信号描述（可选）
        """
        self.signal_name = signal_name
        self.timeframe = timeframe
        self.signal_time = signal_time
        self.signal_values = signal_values or {}
        self.signal_strength = signal_strength
        self.signal_direction = signal_direction
        self.description = description
        self.created_time = datetime.now()
        self.signal_type = self._infer_signal_type(signal_name)

    def _infer_signal_type(self, signal_name):
        """根据信号名称推断信号类型"""
        if any(keyword in signal_name for keyword in ['持续', '连续', '始终']):
            return 'continuous'  # 持续性信号
        elif any(keyword in signal_name for keyword in ['上穿', '下穿', '突破']):
            return 'crossover'   # 穿越性信号
        elif any(keyword in signal_name for keyword in ['成交量', 'MAVOL', 'VOL', '放量', '缩量', 'K线的']):
            return 'volume'      # 成交量信号
        elif any(keyword in signal_name for keyword in ['拐头', '开始', '变长', '缩短']):
            return 'turning'     # 拐点信号
        else:
            return 'general'     # 一般信号
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'signal_name': self.signal_name,
            'timeframe': self.timeframe,
            'signal_time': self.signal_time,
            'signal_values': self.signal_values,
            'signal_strength': self.signal_strength,
            'signal_direction': self.signal_direction,
            'description': self.description,
            'created_time': self.created_time.isoformat()
        }
    
    def __str__(self):
        """字符串表示 - 根据信号类型采用不同格式，与原始程序保持一致"""
        return self._format_signal_output()

    def _format_signal_output(self):
        """根据信号类型格式化输出"""
        if self.signal_type == 'continuous':
            return self._format_continuous_signal()
        elif self.signal_type == 'crossover':
            return self._format_crossover_signal()
        elif self.signal_type == 'volume':
            return self._format_volume_signal()
        elif self.signal_type == 'turning':
            return self._format_turning_signal()
        else:
            return self._format_general_signal()

    def _format_continuous_signal(self):
        """格式化持续性信号 - 如：DIF持续下降：2025-07-22~2025-07-28 DIF值=0.2325,0.3005,0.3433,0.3879,0.4146"""
        # 新格式：time_range + value_sequence
        if 'time_range' in self.signal_values and 'value_sequence' in self.signal_values:
            time_range = self.signal_values['time_range']
            value_sequence = self.signal_values['value_sequence']

            # 格式化时间范围
            start_time = time_range['start'].strftime('%Y-%m-%d') if hasattr(time_range['start'], 'strftime') else clean_timestamp_str(time_range['start'])
            end_time = time_range['end'].strftime('%Y-%m-%d') if hasattr(time_range['end'], 'strftime') else clean_timestamp_str(time_range['end'])

            # 格式化数值序列
            if isinstance(value_sequence, list):
                values_str = ','.join([f"{v:.4f}" for v in value_sequence])
            else:
                values_str = str(value_sequence)

            # 确定指标名称
            indicator_name = self._get_indicator_name_from_signal()

            return f"{self.signal_name}：{start_time}~{end_time} {indicator_name}值={values_str}"

        # 旧格式：start_date + end_date + dif_values/其他values
        elif 'start_date' in self.signal_values and 'end_date' in self.signal_values:
            start_date = self.signal_values['start_date']
            end_date = self.signal_values['end_date']

            # 确定指标名称和数值序列
            indicator_name = self._get_indicator_name_from_signal()

            # 查找数值序列
            value_sequence = None
            if 'dif_values' in self.signal_values:
                value_sequence = self.signal_values['dif_values']
            elif 'macd_values' in self.signal_values:
                value_sequence = self.signal_values['macd_values']
            elif 'values' in self.signal_values:
                value_sequence = self.signal_values['values']

            if value_sequence and isinstance(value_sequence, list):
                values_str = ','.join([f"{v:.4f}" for v in value_sequence])
                return f"{self.signal_name}：{start_date}~{end_date} {indicator_name}值={values_str}"

        return self._format_general_signal()

    def _format_crossover_signal(self):
        """格式化穿越性信号 - 如：RSI6下穿50：2025-07-24 RSI6 51.37→42.75 或 MA5下穿MA10：2025-07-17 MA5 44.95→44.88"""
        if 'cross_date' in self.signal_values and 'before_value' in self.signal_values and 'after_value' in self.signal_values:
            cross_date = self.signal_values['cross_date']
            before_value = self.signal_values['before_value']
            after_value = self.signal_values['after_value']

            # 格式化日期
            date_str = cross_date.strftime('%Y-%m-%d') if hasattr(cross_date, 'strftime') else clean_timestamp_str(cross_date)

            # 确定指标名称
            indicator_name = self._get_indicator_name_from_signal()

            # 对于MA穿越信号，显示MA5的变化
            if 'MA' in self.signal_name and 'MA10' in self.signal_name:
                return f"{self.signal_name}：{date_str} MA5 {before_value:.2f}→{after_value:.2f}"
            else:
                return f"{self.signal_name}：{date_str} {indicator_name} {before_value:.2f}→{after_value:.2f}"
        else:
            # 如果没有穿越数据，尝试其他格式
            time_str = self.signal_time.strftime('%Y-%m-%d') if hasattr(self.signal_time, 'strftime') else clean_timestamp_str(self.signal_time)

            # 检查是否有其他数值数据
            if 'before_value' in self.signal_values and 'after_value' in self.signal_values:
                before_value = self.signal_values['before_value']
                after_value = self.signal_values['after_value']
                indicator_name = self._get_indicator_name_from_signal()
                return f"{self.signal_name}：{time_str} {indicator_name} {before_value:.2f}→{after_value:.2f}"

            return self._format_general_signal()

    def _format_volume_signal(self):
        """格式化成交量信号 - 如：4根及以上K线的MAVOL5上涨：2025-07-25 MAVOL5 772973↑；2025-07-24 MAVOL5 742406↑"""
        if 'volume_details' in self.signal_values:
            details = self.signal_values['volume_details']
            detail_parts = []

            for detail in details:
                date_str = detail['date'].strftime('%Y-%m-%d') if hasattr(detail['date'], 'strftime') else clean_timestamp_str(detail['date'])
                value = detail['value']
                direction = detail.get('direction', '')

                if 'MAVOL' in self.signal_name and 'VOL大于' not in self.signal_name and 'VOL小于' not in self.signal_name:
                    detail_parts.append(f"{date_str} MAVOL5 {value:.0f}{direction}")
                elif 'VOL' in self.signal_name and '大于' in self.signal_name:
                    detail_parts.append(f"{date_str} VOL {value:.0f} > MAVOL5 {detail.get('mavol5', 0):.0f}")
                elif 'VOL' in self.signal_name and '小于' in self.signal_name:
                    detail_parts.append(f"{date_str} VOL {value:.0f} < MAVOL5 {detail.get('mavol5', 0):.0f}")

            if detail_parts:
                return f"{self.signal_name}：{' ；'.join(detail_parts)}"

        return self._format_general_signal()

    def _format_turning_signal(self):
        """格式化拐点信号 - 如：MACD红柱开始缩短：2025-07-11 MACD 0.0820→0.0516"""
        time_str = self.signal_time.strftime('%Y-%m-%d') if hasattr(self.signal_time, 'strftime') else clean_timestamp_str(self.signal_time)

        # MACD红柱缩短信号
        if 'MACD红柱开始缩短' in self.signal_name and 'peak_value' in self.signal_values and 'shrink_start_value' in self.signal_values:
            peak_value = self.signal_values['peak_value']
            shrink_value = self.signal_values['shrink_start_value']
            return f"{self.signal_name}：{time_str} MACD {peak_value:.4f}→{shrink_value:.4f}"

        # MACD红柱持续变长信号
        elif 'MACD红柱持续变长' in self.signal_name and 'macd_values' in self.signal_values:
            macd_values = self.signal_values['macd_values']
            start_date = self.signal_values.get('start_date', time_str)
            end_date = self.signal_values.get('end_date', time_str)
            values_str = ','.join([f"{v:.4f}" for v in macd_values])
            return f"{self.signal_name}：{start_date}~{end_date} MACD值={values_str}"

        # DIF拐头向上/向下信号
        elif 'DIF拐头' in self.signal_name and 'before_value' in self.signal_values and 'after_value' in self.signal_values:
            before_value = self.signal_values['before_value']
            after_value = self.signal_values['after_value']
            return f"{self.signal_name}：{time_str} DIF {before_value:.4f}→{after_value:.4f}"

        # MA5拐头向上/向下信号
        elif 'MA5拐头' in self.signal_name and 'before_value' in self.signal_values and 'after_value' in self.signal_values:
            before_value = self.signal_values['before_value']
            after_value = self.signal_values['after_value']
            return f"{self.signal_name}：{time_str} MA5 {before_value:.4f}→{after_value:.4f}"

        return f"{self.signal_name}：{time_str}"

    def _format_general_signal(self):
        """格式化一般信号 - 保持原有格式"""
        time_str = self.signal_time.strftime('%Y-%m-%d') if hasattr(self.signal_time, 'strftime') else clean_timestamp_str(self.signal_time)
        direction_str = f"[{self.signal_direction}]" if self.signal_direction else ""
        return f"{direction_str}{self.signal_name}({self.timeframe}) - {time_str}"

    def _get_indicator_name_from_signal(self):
        """从信号名称中提取指标名称"""
        if 'DIF' in self.signal_name:
            return 'DIF'
        elif 'DEA' in self.signal_name:
            return 'DEA'
        elif 'MACD' in self.signal_name:
            return 'MACD'
        elif 'RSI6' in self.signal_name:
            return 'RSI6'
        elif 'MA5' in self.signal_name:
            return 'MA5'
        elif 'MA10' in self.signal_name:
            return 'MA10'
        elif 'MAVOL5' in self.signal_name:
            return 'MAVOL5'
        else:
            return 'VALUE'


class TechnicalSignalDetector:
    """技术信号检测器"""
    
    def __init__(self):
        """初始化信号检测器"""
        self.data = None
        self.indicators = None
        self.signals = []
        self.timeframe = "日线"
    
    def load_data(self, df, timeframe="日线"):
        """加载K线数据"""
        if df is None or df.empty:
            print("错误: 输入数据为空")
            return False
        
        # 验证必要列是否存在
        required_columns = ['日期', '开盘', '最高', '最低', '收盘', '成交量']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"错误: 缺少必要的数据列: {missing_columns}")
            return False
        
        # 复制数据并确保按日期升序排列
        self.data = df.copy()
        self.data = self.data.sort_values('日期', ascending=True).reset_index(drop=True)
        self.timeframe = timeframe
        
        # 验证数据量充足
        if len(self.data) < 30:
            print(f"警告: 数据量较少({len(self.data)}条)，可能影响信号检测准确性")
        
        print(f"? 成功加载K线数据: {len(self.data)} 条记录 ({timeframe})")
        return True
    
    def calculate_indicators(self):
        """计算技术指标"""
        if not ZZINDICATOR_AVAILABLE:
            print("错误: ZZindicator模块不可用，无法计算技术指标")
            return False
        
        if self.data is None:
            print("错误: 未加载数据，无法计算指标")
            return False
        
        try:
            # 使用ZZindicator计算技术指标
            result = calculate_indicators_from_dataframe(self.data)

            if result:
                # 检查返回结果的格式
                if isinstance(result, dict) and 'indicators' in result:
                    # 新格式：包含indicators键的字典
                    self.indicators = result['indicators']
                else:
                    # 直接返回指标字典的格式
                    self.indicators = result

                print("? 技术指标计算完成")
                return True
            else:
                print("错误: 技术指标计算失败")
                return False

        except Exception as e:
            print(f"计算技术指标时出错: {e}")
            return False
    
    def _format_date(self, date_val, period_label="日"):
        """格式化日期显示，去除.000000000后缀"""
        if hasattr(date_val, 'strftime'):
            if period_label == "30分":
                return date_val.strftime('%m-%d %H:%M')
            else:
                return date_val.strftime('%m-%d')
        else:
            # 处理numpy.datetime64等类型，去除.000000000后缀
            date_str = str(date_val)
            # 去除.000000000后缀
            date_str = date_str.replace('.000000000', '')
            # 如果是ISO格式，转换为更友好的格式
            if 'T' in date_str:
                try:
                    # 尝试解析ISO格式并重新格式化
                    import pandas as pd
                    parsed_date = pd.to_datetime(date_str)
                    if period_label == "30分":
                        return parsed_date.strftime('%m-%d %H:%M')
                    else:
                        return parsed_date.strftime('%m-%d')
                except:
                    # 如果解析失败，直接返回清理后的字符串
                    pass
            return date_str
    
    def detect_macd_signals(self):
        """检测MACD相关信号"""
        if not self.indicators or 'MACD' not in self.indicators:
            return []
        
        macd_data = self.indicators['MACD']
        dif_values = macd_data['DIF'].values
        dea_values = macd_data['DEA'].values
        macd_values = macd_data['MACD'].values
        dates = self.data['日期'].values
        
        signals = []
        
        # 确保数据按时间升序排列（最新数据在最后）
        # 为了与原始程序逻辑一致，我们需要反转数组来模拟"最新在前"的逻辑
        dif_reversed = dif_values[::-1]  # 最新在前
        dea_reversed = dea_values[::-1]  # 最新在前
        macd_reversed = macd_values[::-1]  # 最新在前
        dates_reversed = dates[::-1]  # 最新在前
        
        # 1. DIF拐头向上检测
        if len(dif_reversed) >= 5:
            for i in range(1, min(4, len(dif_reversed) - 1)):
                if (dif_reversed[i] < dif_reversed[i+1] and dif_reversed[i] < dif_reversed[i-1]):
                    # 确认拐点后的持续上涨
                    if all(dif_reversed[j] > dif_reversed[j+1] for j in range(i)):
                        completion_date_idx = max(0, i-1) if i > 0 else 0
                        signal_time = dates_reversed[completion_date_idx]
                        
                        signal = TechnicalSignal(
                            signal_name="DIF拐头向上",
                            timeframe=self.timeframe,
                            signal_time=signal_time,
                            signal_values={
                                # 修复：拐头向上应该显示从低点到高点的变化
                                'before_value': float(dif_reversed[i]),  # 拐点处的DIF值（最低点）
                                'after_value': float(dif_reversed[i-1] if i-1 >= 0 else dif_reversed[0]),  # 拐头后的DIF值（上升后的值）
                                'DIF': float(dif_reversed[0]),  # 当前DIF值
                                'DEA': float(dea_reversed[0]),  # 当前DEA值
                                'MACD': float(macd_reversed[0]),  # 当前MACD值
                                'dif_before': float(dif_reversed[i]),
                                'dif_after': float(dif_reversed[i-1] if i-1 >= 0 else dif_reversed[0]),
                                'completion_date': self._format_date(signal_time)
                            },
                            signal_direction="看涨",
                            description=f"DIF在{self._format_date(signal_time)}完成拐头向上"
                        )
                        signals.append(signal)
                        break
        
        # 2. DIF持续上涨检测
        if len(dif_reversed) >= 5:
            dif_continuous_rising = all(dif_reversed[i] > dif_reversed[i+1] for i in range(4))
            if dif_continuous_rising:
                signal = TechnicalSignal(
                    signal_name="DIF持续上涨",
                    timeframe=self.timeframe,
                    signal_time=dates_reversed[0],
                    signal_values={
                        'time_range': {
                            'start': dates_reversed[4],
                            'end': dates_reversed[0]
                        },
                        'value_sequence': [float(v) for v in dif_reversed[:5]],
                        'DIF': float(dif_reversed[0]),  # 当前DIF值
                        'DEA': float(dea_reversed[0]),  # 当前DEA值
                        'MACD': float(macd_reversed[0]),  # 当前MACD值
                        'dif_values': [float(v) for v in dif_reversed[:5]],
                        'start_date': self._format_date(dates_reversed[4]),
                        'end_date': self._format_date(dates_reversed[0])
                    },
                    signal_direction="看涨",
                    description=f"DIF从{self._format_date(dates_reversed[4])}到{self._format_date(dates_reversed[0])}持续上涨"
                )
                signals.append(signal)

        # 3. DIF拐头向下检测
        if len(dif_reversed) >= 5:
            for i in range(1, min(4, len(dif_reversed) - 1)):
                if (dif_reversed[i] > dif_reversed[i+1] and dif_reversed[i] > dif_reversed[i-1]):
                    # 确认拐点后的持续下跌
                    if all(dif_reversed[j] < dif_reversed[j+1] for j in range(i)):
                        completion_date_idx = max(0, i-1) if i > 0 else 0
                        signal_time = dates_reversed[completion_date_idx]

                        signal = TechnicalSignal(
                            signal_name="DIF拐头向下",
                            timeframe=self.timeframe,
                            signal_time=signal_time,
                            signal_values={
                                'dif_before': float(dif_reversed[i+1]),
                                'dif_after': float(dif_reversed[i-1] if i-1 >= 0 else dif_reversed[i]),
                                'completion_date': self._format_date(signal_time)
                            },
                            signal_direction="看跌",
                            description=f"DIF在{self._format_date(signal_time)}完成拐头向下"
                        )
                        signals.append(signal)
                        break

        # 4. DIF持续下降检测
        if len(dif_reversed) >= 5:
            dif_continuous_falling = all(dif_reversed[i] < dif_reversed[i+1] for i in range(4))
            if dif_continuous_falling:
                signal = TechnicalSignal(
                    signal_name="DIF持续下降",
                    timeframe=self.timeframe,
                    signal_time=dates_reversed[0],
                    signal_values={
                        'time_range': {
                            'start': dates_reversed[4],
                            'end': dates_reversed[0]
                        },
                        'value_sequence': [float(v) for v in dif_reversed[:5]],
                        'DIF': float(dif_reversed[0]),  # 当前DIF值
                        'DEA': float(dea_reversed[0]),  # 当前DEA值
                        'MACD': float(macd_reversed[0]),  # 当前MACD值
                    },
                    signal_direction="看跌",
                    description=f"DIF从{self._format_date(dates_reversed[4])}到{self._format_date(dates_reversed[0])}持续下降"
                )
                signals.append(signal)

        # 5. MACD金叉/死叉
        if len(dif_reversed) >= 5 and len(dea_reversed) >= 5:
            last_golden_cross_idx = -1
            last_death_cross_idx = -1

            # 寻找最近的金叉
            for i in range(4):
                if (dif_reversed[i] > dea_reversed[i] and dif_reversed[i+1] <= dea_reversed[i+1]):
                    if all(dif_reversed[j] > dea_reversed[j] for j in range(i + 1)):
                        last_golden_cross_idx = i
                        break

            # 寻找最近的死叉
            for i in range(4):
                if (dif_reversed[i] < dea_reversed[i] and dif_reversed[i+1] >= dea_reversed[i+1]):
                    if all(dif_reversed[j] < dea_reversed[j] for j in range(i + 1)):
                        last_death_cross_idx = i
                        break

            # 比较哪个信号更近
            if last_golden_cross_idx != -1 and last_death_cross_idx != -1:
                if last_golden_cross_idx < last_death_cross_idx:
                    signal = TechnicalSignal(
                        signal_name="MACD金叉",
                        timeframe=self.timeframe,
                        signal_time=dates_reversed[last_golden_cross_idx],
                        signal_values={
                            'dif_value': float(dif_reversed[last_golden_cross_idx]),
                            'dea_value': float(dea_reversed[last_golden_cross_idx]),
                            'cross_date': self._format_date(dates_reversed[last_golden_cross_idx])
                        },
                        signal_direction="看涨",
                        description=f"MACD在{self._format_date(dates_reversed[last_golden_cross_idx])}形成金叉"
                    )
                    signals.append(signal)
                else:
                    signal = TechnicalSignal(
                        signal_name="MACD死叉",
                        timeframe=self.timeframe,
                        signal_time=dates_reversed[last_death_cross_idx],
                        signal_values={
                            'dif_value': float(dif_reversed[last_death_cross_idx]),
                            'dea_value': float(dea_reversed[last_death_cross_idx]),
                            'cross_date': self._format_date(dates_reversed[last_death_cross_idx])
                        },
                        signal_direction="看跌",
                        description=f"MACD在{self._format_date(dates_reversed[last_death_cross_idx])}形成死叉"
                    )
                    signals.append(signal)
            elif last_golden_cross_idx != -1:
                signal = TechnicalSignal(
                    signal_name="MACD金叉",
                    timeframe=self.timeframe,
                    signal_time=dates_reversed[last_golden_cross_idx],
                    signal_values={
                        'dif_value': float(dif_reversed[last_golden_cross_idx]),
                        'dea_value': float(dea_reversed[last_golden_cross_idx]),
                        'cross_date': self._format_date(dates_reversed[last_golden_cross_idx])
                    },
                    signal_direction="看涨",
                    description=f"MACD在{self._format_date(dates_reversed[last_golden_cross_idx])}形成金叉"
                )
                signals.append(signal)
            elif last_death_cross_idx != -1:
                signal = TechnicalSignal(
                    signal_name="MACD死叉",
                    timeframe=self.timeframe,
                    signal_time=dates_reversed[last_death_cross_idx],
                    signal_values={
                        'dif_value': float(dif_reversed[last_death_cross_idx]),
                        'dea_value': float(dea_reversed[last_death_cross_idx]),
                        'cross_date': self._format_date(dates_reversed[last_death_cross_idx])
                    },
                    signal_direction="看跌",
                    description=f"MACD在{self._format_date(dates_reversed[last_death_cross_idx])}形成死叉"
                )
                signals.append(signal)

        return signals

    def detect_ma_signals(self):
        """检测移动平均线信号（扩展到15个周期）"""
        if not self.indicators or 'MA' not in self.indicators:
            return []

        ma_data = self.indicators['MA']

        # 检查MA数据是否存在（修复键名）
        if 'MA5' not in ma_data or 'MA10' not in ma_data:
            print("警告: MA5或MA10数据不存在，跳过移动平均线信号检测")
            return []

        ma5_values = ma_data['MA5'].values
        ma10_values = ma_data['MA10'].values
        close_prices = self.data['收盘'].values
        dates = self.data['日期'].values

        signals = []

        # 反转数组以匹配原始逻辑（最新在前）
        ma5_reversed = ma5_values[::-1]
        ma10_reversed = ma10_values[::-1]
        close_reversed = close_prices[::-1]
        dates_reversed = dates[::-1]

        # 扩展监控范围到15个周期
        monitor_range = min(15, len(ma5_reversed))

        # 1. MA5拐头向上（扩展到15个周期）
        if len(ma5_reversed) >= 15:
            for i in range(1, min(14, len(ma5_reversed) - 1)):
                if (ma5_reversed[i] < ma5_reversed[i+1] and ma5_reversed[i] < ma5_reversed[i-1]):
                    if all(ma5_reversed[j] > ma5_reversed[j+1] for j in range(i)):
                        signal = TechnicalSignal(
                            signal_name="MA5拐头向上",
                            timeframe=self.timeframe,
                            signal_time=dates_reversed[max(0, i-1)],
                            signal_values={
                                # 修复：拐头向上应该显示从低点到高点的变化
                                'before_value': float(ma5_reversed[i]),      # 拐点处的值（最低点）
                                'after_value': float(ma5_reversed[i-1] if i-1 >= 0 else ma5_reversed[0]),  # 拐头后的值（上升后的值）
                                'ma5_before': float(ma5_reversed[i]),
                                'ma5_after': float(ma5_reversed[i-1] if i-1 >= 0 else ma5_reversed[0])
                            },
                            signal_direction="看涨",
                            description=f"MA5在{self._format_date(dates_reversed[max(0, i-1)])}完成拐头向上"
                        )
                        signals.append(signal)
                        break

        # 2. MA5上穿MA10（金叉）（扩展到15个周期）
        if len(ma5_reversed) >= 15 and len(ma10_reversed) >= 15:
            for i in range(14):
                if (ma5_reversed[i] > ma10_reversed[i] and ma5_reversed[i+1] <= ma10_reversed[i+1]):
                    if all(ma5_reversed[j] > ma10_reversed[j] for j in range(i + 1)):
                        signal = TechnicalSignal(
                            signal_name="MA5上穿MA10",
                            timeframe=self.timeframe,
                            signal_time=dates_reversed[i],
                            signal_values={
                                'cross_date': dates_reversed[i],
                                'before_value': float(ma5_reversed[i+1]),  # 穿越前MA5值
                                'after_value': float(ma5_reversed[i]),     # 穿越后MA5值
                                'ma10_value': float(ma10_reversed[i]),     # 穿越时MA10值
                                'MA5': float(ma5_reversed[i]),  # 信号触发时的MA5值
                                'MA10': float(ma10_reversed[i]),  # 信号触发时的MA10值
                                'ma5_value': float(ma5_reversed[i]),
                                'ma10_value': float(ma10_reversed[i])
                            },
                            signal_direction="看涨",
                            description=f"MA5在{self._format_date(dates_reversed[i])}上穿MA10"
                        )
                        signals.append(signal)
                        break

        # 3. MA5下穿MA10（死叉）（扩展到15个周期）
        if len(ma5_reversed) >= 15 and len(ma10_reversed) >= 15:
            for i in range(14):
                if (ma5_reversed[i] < ma10_reversed[i] and ma5_reversed[i+1] >= ma10_reversed[i+1]):
                    if all(ma5_reversed[j] < ma10_reversed[j] for j in range(i + 1)):
                        signal = TechnicalSignal(
                            signal_name="MA5下穿MA10",
                            timeframe=self.timeframe,
                            signal_time=dates_reversed[i],
                            signal_values={
                                'cross_date': dates_reversed[i],
                                'before_value': float(ma5_reversed[i+1]),  # 穿越前MA5值
                                'after_value': float(ma5_reversed[i]),     # 穿越后MA5值
                                'ma10_value': float(ma10_reversed[i]),     # 穿越时MA10值
                                'MA5': float(ma5_reversed[i]),  # 信号触发时的MA5值
                                'MA10': float(ma10_reversed[i]),  # 信号触发时的MA10值
                                'ma5_value': float(ma5_reversed[i]),
                                'ma10_value': float(ma10_reversed[i])
                            },
                            signal_direction="看跌",
                            description=f"MA5在{self._format_date(dates_reversed[i])}下穿MA10"
                        )
                        signals.append(signal)
                        break

        # 4. 股价持续运行在MA5之上（扩展到15个周期）
        if len(close_reversed) >= 15 and len(ma5_reversed) >= 15:
            price_above_ma5 = all(close_reversed[i] > ma5_reversed[i] for i in range(15))
            if price_above_ma5:
                signal = TechnicalSignal(
                    signal_name="股价持续运行在MA5之上",
                    timeframe=self.timeframe,
                    signal_time=dates_reversed[0],
                    signal_values={
                        'start_date': self._format_date(dates_reversed[14]),
                        'end_date': self._format_date(dates_reversed[0]),
                        'price_values': [float(v) for v in close_reversed[:15]],
                        'ma5_values': [float(v) for v in ma5_reversed[:15]]
                    },
                    signal_direction="看涨",
                    description=f"股价从{self._format_date(dates_reversed[14])}到{self._format_date(dates_reversed[0])}持续运行在MA5之上"
                )
                signals.append(signal)

        return signals

    def detect_rsi_signals(self):
        """检测RSI信号（扩展到15个周期）"""
        if not self.indicators or 'RSI' not in self.indicators:
            return []

        rsi_data = self.indicators['RSI']

        # 检查RSI数据是否存在（修复键名）
        if 'RSI6' not in rsi_data:
            print("警告: RSI6数据不存在，跳过RSI信号检测")
            return []

        rsi6_values = rsi_data['RSI6'].values
        dates = self.data['日期'].values

        signals = []

        # 反转数组以匹配原始逻辑（最新在前）
        rsi6_reversed = rsi6_values[::-1]
        dates_reversed = dates[::-1]

        # 扩展监控范围到15个周期
        if len(rsi6_reversed) < 15:
            return signals

        # 1. RSI6上穿50（扩展到15个周期）
        for i in range(14):
            if rsi6_reversed[i] > 50 and rsi6_reversed[i+1] <= 50:
                # 检查之后是否持续在50上方
                stayed_above = all(rsi6_reversed[j] > 50 for j in range(i + 1))
                if stayed_above:
                    signal = TechnicalSignal(
                        signal_name="RSI6上穿50",
                        timeframe=self.timeframe,
                        signal_time=dates_reversed[i],
                        signal_values={
                            'cross_date': dates_reversed[i],
                            'before_value': float(rsi6_reversed[i+1]),  # 穿越前RSI6值
                            'after_value': float(rsi6_reversed[i]),     # 穿越后RSI6值
                            'rsi_before': float(rsi6_reversed[i+1]),
                            'rsi_after': float(rsi6_reversed[i])
                        },
                        signal_direction="看涨",
                        description=f"RSI6在{self._format_date(dates_reversed[i])}上穿50"
                    )
                    signals.append(signal)
                    break

        # 2. RSI6下穿50（扩展到15个周期）
        for i in range(14):
            if rsi6_reversed[i] < 50 and rsi6_reversed[i+1] >= 50:
                # 检查之后是否持续在50下方
                stayed_below = all(rsi6_reversed[j] < 50 for j in range(i + 1))
                if stayed_below:
                    signal = TechnicalSignal(
                        signal_name="RSI6下穿50",
                        timeframe=self.timeframe,
                        signal_time=dates_reversed[i],
                        signal_values={
                            'cross_date': dates_reversed[i],
                            'before_value': float(rsi6_reversed[i+1]),
                            'after_value': float(rsi6_reversed[i]),
                            'RSI6': float(rsi6_reversed[i]),  # 信号触发时的RSI6值
                            'rsi_before': float(rsi6_reversed[i+1]),
                            'rsi_after': float(rsi6_reversed[i])
                        },
                        signal_direction="看跌",
                        description=f"RSI6在{self._format_date(dates_reversed[i])}下穿50"
                    )
                    signals.append(signal)
                    break

        # 3. RSI6始终运行在50以上（扩展到15个周期）
        rsi6_above_50 = all(rsi > 50 for rsi in rsi6_reversed[:15])
        if rsi6_above_50:
            signal = TechnicalSignal(
                signal_name="RSI6始终运行在50以上",
                timeframe=self.timeframe,
                signal_time=dates_reversed[0],
                signal_values={
                    'start_date': self._format_date(dates_reversed[14]),
                    'end_date': self._format_date(dates_reversed[0]),
                    'rsi_values': [float(v) for v in rsi6_reversed[:15]]
                },
                signal_direction="看涨",
                description=f"RSI6从{self._format_date(dates_reversed[14])}到{self._format_date(dates_reversed[0])}始终运行在50以上"
            )
            signals.append(signal)

        return signals

    def detect_macd_extended_signals(self):
        """检测MACD扩展信号（扩展到15个周期）"""
        if not self.indicators or 'MACD' not in self.indicators:
            return []

        macd_data = self.indicators['MACD']
        dif_values = macd_data['DIF'].values
        dea_values = macd_data['DEA'].values
        macd_values = macd_data['MACD'].values
        dates = self.data['日期'].values

        signals = []

        # 反转数组以匹配原始逻辑（最新在前）
        dif_reversed = dif_values[::-1]
        dea_reversed = dea_values[::-1]
        macd_reversed = macd_values[::-1]
        dates_reversed = dates[::-1]

        # 扩展监控范围到15个周期
        if len(dif_reversed) < 15:
            return signals

        # 1. MACD红柱持续变长（扩展到15个周期）
        if len(macd_reversed) >= 15:
            # 检查前4个周期的红柱是否持续变长
            for start_idx in range(11):  # 可以从0到10开始检查4个周期
                if all(macd_reversed[i] > 0 for i in range(start_idx, start_idx + 4)):
                    if all(macd_reversed[i] > macd_reversed[i+1] for i in range(start_idx, start_idx + 3)):
                        signal = TechnicalSignal(
                            signal_name="MACD红柱持续变长",
                            timeframe=self.timeframe,
                            signal_time=dates_reversed[start_idx],
                            signal_values={
                                'start_date': self._format_date(dates_reversed[start_idx + 3]),
                                'end_date': self._format_date(dates_reversed[start_idx]),
                                'macd_values': [float(v) for v in macd_reversed[start_idx:start_idx + 4]]
                            },
                            signal_direction="看涨",
                            description=f"MACD红柱从{self._format_date(dates_reversed[start_idx + 3])}到{self._format_date(dates_reversed[start_idx])}持续变长"
                        )
                        signals.append(signal)
                        break

        # 2. MACD红柱开始缩短（扩展到15个周期）
        if len(macd_reversed) >= 15:
            # 寻找红柱缩短的起始点 - 采用与原始程序相同的逻辑
            for i in range(14):  # 检查前14个周期
                if (macd_reversed[i] > 0 and macd_reversed[i+1] > 0 and
                    macd_reversed[i] < macd_reversed[i+1]):

                    # 找到了一个缩短点，现在寻找峰值点
                    peak_idx = i + 1  # 默认前一个点为峰值

                    # 向后查找真正的峰值点
                    for j in range(i + 2, min(len(macd_reversed), i + 6)):
                        if macd_reversed[j] > 0:  # 确保还是红柱
                            if macd_reversed[j] > macd_reversed[peak_idx]:
                                peak_idx = j
                        else:
                            break  # 如果不是红柱，停止查找

                    # 从峰值点开始，找到第一个下降的时间点作为缩短开始点
                    shrink_start_idx = i
                    for k in range(peak_idx - 1, -1, -1):  # 从峰值向前查找
                        if k >= 0 and macd_reversed[k] < macd_reversed[k + 1]:
                            shrink_start_idx = k
                            break

                    signal = TechnicalSignal(
                        signal_name="MACD红柱开始缩短",
                        timeframe=self.timeframe,
                        signal_time=dates_reversed[shrink_start_idx],
                        signal_values={
                            'shrink_start_date': dates_reversed[shrink_start_idx],
                            'peak_date': dates_reversed[peak_idx],
                            'peak_value': float(macd_reversed[peak_idx]),
                            'shrink_start_value': float(macd_reversed[shrink_start_idx]),
                            'current_value': float(macd_reversed[0]),
                            'DIF': float(dif_reversed[shrink_start_idx]),
                            'DEA': float(dea_reversed[shrink_start_idx]),
                            'MACD': float(macd_reversed[shrink_start_idx])
                        },
                        signal_direction="中性",
                        description=f"MACD红柱在{self._format_date(dates_reversed[shrink_start_idx])}开始缩短"
                    )
                    signals.append(signal)
                    break  # 找到第一个缩短信号后退出

        # 3. DIF上穿0轴或持续在0轴上方（扩展到15个周期）
        # 检查DIF上穿0轴
        dif_crossed_up = False
        for i in range(14):
            if dif_reversed[i] > 0 and dif_reversed[i+1] <= 0:
                # 确认上穿后持续在0轴上方
                if all(dif_reversed[j] > 0 for j in range(i+1)):
                    dif_crossed_up = True
                    signal = TechnicalSignal(
                        signal_name="DIF上穿0轴且持续在上方",
                        timeframe=self.timeframe,
                        signal_time=dates_reversed[i],
                        signal_values={
                            'cross_date': self._format_date(dates_reversed[i]),
                            'dif_values': [float(v) for v in dif_reversed[:15]]
                        },
                        signal_direction="看涨",
                        description=f"DIF在{self._format_date(dates_reversed[i])}上穿0轴且持续在上方"
                    )
                    signals.append(signal)
                    break

        # 检查DIF持续在0轴上方
        if not dif_crossed_up:
            dif_above_zero = all(dif > 0 for dif in dif_reversed[:15])
            if dif_above_zero:
                signal = TechnicalSignal(
                    signal_name="DIF持续在0轴上方",
                    timeframe=self.timeframe,
                    signal_time=dates_reversed[0],
                    signal_values={
                        'start_date': self._format_date(dates_reversed[14]),
                        'end_date': self._format_date(dates_reversed[0]),
                        'dif_values': [float(v) for v in dif_reversed[:15]]
                    },
                    signal_direction="看涨",
                    description=f"DIF从{self._format_date(dates_reversed[14])}到{self._format_date(dates_reversed[0])}持续在0轴上方"
                )
                signals.append(signal)

        # 4. DIF值持续下降（扩展到15个周期）
        if len(dif_reversed) >= 15:
            dif_continuous_falling = all(dif_reversed[i] < dif_reversed[i+1] for i in range(14))
            if dif_continuous_falling:
                signal = TechnicalSignal(
                    signal_name="DIF值持续下降",
                    timeframe=self.timeframe,
                    signal_time=dates_reversed[0],
                    signal_values={
                        'start_date': self._format_date(dates_reversed[14]),
                        'end_date': self._format_date(dates_reversed[0]),
                        'dif_values': [float(v) for v in dif_reversed[:15]]
                    },
                    signal_direction="看跌",
                    description=f"DIF从{self._format_date(dates_reversed[14])}到{self._format_date(dates_reversed[0])}持续下降"
                )
                signals.append(signal)

        return signals

    def detect_volume_signals(self):
        """检测成交量信号（保持5个周期）"""
        if not self.indicators or 'MAVOL' not in self.indicators:
            return []

        volume_ma_data = self.indicators['MAVOL']

        # 检查成交量数据是否存在（修复键名）
        if 'MAVOL5' not in volume_ma_data:
            print("警告: MAVOL5数据不存在，跳过成交量信号检测")
            return []

        volume_values = self.data['成交量'].values
        mavol5_values = volume_ma_data['MAVOL5'].values
        dates = self.data['日期'].values

        signals = []

        # 反转数组以匹配原始逻辑（最新在前）
        volume_reversed = volume_values[::-1]
        mavol5_reversed = mavol5_values[::-1]
        dates_reversed = dates[::-1]

        # 成交量信号检查最近5根K线（包含最新K线），需要至少5根K线数据
        if len(volume_reversed) < 5:
            return signals

        # 1. 曾出现放量超过MAVOL5两倍以上的K线（检查最近5根K线，包含最新K线）
        found_heavy_volume = False
        heavy_volume_details = []
        for i in range(0, 5):  # 修正：从最新K线开始检查，包含最新K线
            if volume_reversed[i] > mavol5_reversed[i] * 2:
                found_heavy_volume = True
                heavy_volume_details.append({
                    'date': self._format_date(dates_reversed[i]),
                    'volume': float(volume_reversed[i]),
                    'mavol5': float(mavol5_reversed[i])
                })

        if found_heavy_volume:
            signal = TechnicalSignal(
                signal_name="放量超过MAVOL5两倍以上",
                timeframe=self.timeframe,
                signal_time=dates_reversed[0],  # 修正：使用最新K线时间
                signal_values={
                    'heavy_volume_details': heavy_volume_details
                },
                signal_direction="中性",
                description=f"近期出现放量超过MAVOL5两倍以上的K线"
            )
            signals.append(signal)

        # 2. 3根及以上K线的MAVOL5上涨（检查最近4根K线，包含最新K线）
        mavol5_rising_count = 0
        rising_details = []
        for i in range(0, 4):  # 修正：从最新K线开始检查，包含最新K线
            if mavol5_reversed[i] > mavol5_reversed[i+1]:
                mavol5_rising_count += 1
                rising_details.append({
                    'date': dates_reversed[i],
                    'value': float(mavol5_reversed[i]),
                    'direction': '↑'
                })

        if mavol5_rising_count >= 3:
            signal = TechnicalSignal(
                signal_name="3根及以上K线的MAVOL5上涨",
                timeframe=self.timeframe,
                signal_time=dates_reversed[0],  # 修正：使用最新K线时间
                signal_values={
                    'volume_details': rising_details,
                    'VOLUME': float(volume_reversed[0]),  # 修正：使用最新K线的成交量
                    'MAVOL5': float(mavol5_reversed[0]),  # 修正：使用最新K线的MAVOL5
                    'rising_count': mavol5_rising_count,
                    'rising_details': rising_details
                },
                signal_direction="看涨",
                description=f"近{mavol5_rising_count}根K线的MAVOL5上涨"
            )
            signals.append(signal)

        # 3. 3根及以上K线的MAVOL5下跌（检查最近4根K线，包含最新K线）
        mavol5_falling_count = 0
        falling_details = []
        for i in range(0, 4):  # 修正：从最新K线开始检查，包含最新K线
            if mavol5_reversed[i] < mavol5_reversed[i+1]:
                mavol5_falling_count += 1
                falling_details.append({
                    'date': self._format_date(dates_reversed[i]),
                    'mavol5': float(mavol5_reversed[i])
                })

        if mavol5_falling_count >= 3:
            signal = TechnicalSignal(
                signal_name="3根及以上K线的MAVOL5下跌",
                timeframe=self.timeframe,
                signal_time=dates_reversed[0],  # 修正：使用最新K线时间
                signal_values={
                    'falling_count': mavol5_falling_count,
                    'falling_details': falling_details
                },
                signal_direction="看跌",
                description=f"近{mavol5_falling_count}根K线的MAVOL5下跌"
            )
            signals.append(signal)

        # 4. 4根及以上K线的VOL大于MAVOL5（检查最近5根K线，包含最新K线）
        vol_above_mavol5_count = 0
        vol_above_details = []
        for i in range(0, 5):  # 修正：从最新K线开始检查，包含最新K线
            if volume_reversed[i] > mavol5_reversed[i]:
                vol_above_mavol5_count += 1
                vol_above_details.append({
                    'date': dates_reversed[i],
                    'value': float(volume_reversed[i]),
                    'mavol5': float(mavol5_reversed[i])
                })

        if vol_above_mavol5_count >= 4:
            signal = TechnicalSignal(
                signal_name="4根及以上K线的VOL大于MAVOL5",
                timeframe=self.timeframe,
                signal_time=dates_reversed[0],  # 修正：使用最新K线时间
                signal_values={
                    'volume_details': vol_above_details,
                    'VOLUME': float(volume_reversed[0]),  # 修正：使用最新K线的成交量
                    'MAVOL5': float(mavol5_reversed[0]),  # 修正：使用最新K线的MAVOL5
                    'above_count': vol_above_mavol5_count,
                    'above_details': vol_above_details
                },
                signal_direction="看涨",
                description=f"近{vol_above_mavol5_count}根K线的VOL大于MAVOL5"
            )
            signals.append(signal)

        # 5. 4根及以上K线的VOL小于MAVOL5（检查最近5根K线，包含最新K线）
        vol_below_mavol5_count = 0
        vol_below_details = []
        for i in range(0, 5):  # 修正：从最新K线开始检查，包含最新K线
            if volume_reversed[i] < mavol5_reversed[i]:
                vol_below_mavol5_count += 1
                vol_below_details.append({
                    'date': self._format_date(dates_reversed[i]),
                    'volume': float(volume_reversed[i]),
                    'mavol5': float(mavol5_reversed[i])
                })

        if vol_below_mavol5_count >= 4:
            signal = TechnicalSignal(
                signal_name="4根及以上K线的VOL小于MAVOL5",
                timeframe=self.timeframe,
                signal_time=dates_reversed[0],  # 修正：使用最新K线时间
                signal_values={
                    'below_count': vol_below_mavol5_count,
                    'below_details': vol_below_details
                },
                signal_direction="看跌",
                description=f"近{vol_below_mavol5_count}根K线的VOL小于MAVOL5"
            )
            signals.append(signal)

        return signals

    def detect_all_signals(self):
        """检测所有技术信号"""
        self.signals = []

        # 检测MACD信号
        macd_signals = self.detect_macd_signals()
        self.signals.extend(macd_signals)

        # 检测移动平均线信号
        ma_signals = self.detect_ma_signals()
        self.signals.extend(ma_signals)

        # 检测RSI信号
        rsi_signals = self.detect_rsi_signals()
        self.signals.extend(rsi_signals)

        # 检测MACD扩展信号
        macd_extended_signals = self.detect_macd_extended_signals()
        self.signals.extend(macd_extended_signals)

        # 检测成交量信号
        volume_signals = self.detect_volume_signals()
        self.signals.extend(volume_signals)

        return self.signals
    
    def print_signals(self, show_details=True):
        """打印信号分析结果"""
        if not self.signals:
            print("未检测到任何技术信号")
            return
        
        print(f"\n=== 技术信号分析结果 ({self.timeframe}) ===")
        print(f"共检测到 {len(self.signals)} 个技术信号\n")
        
        for i, signal in enumerate(self.signals, 1):
            direction_color = ""
            if signal.signal_direction == "看涨":
                direction_color = "↗"
            elif signal.signal_direction == "看跌":
                direction_color = "↘"
            else:
                direction_color = "→"
            
            print(f"{i}. {direction_color} {signal}")
            
            if show_details and signal.description:
                print(f"   {signal.description}")
        
        return self.signals


def get_stock_data(stock_code, timeframe="日线"):
    """获取股票数据"""
    if not ZZFETCH_AVAILABLE:
        print("错误: ZZfetch模块不可用，无法获取股票数据")
        return None
    
    try:
        # 清理股票代码
        from ZZconfig import DataConfig
        clean_code = DataConfig.clean_stock_code(stock_code)
        
        if not clean_code or len(clean_code) != 6:
            print(f"错误: 无效的股票代码 '{stock_code}'")
            return None
        
        print(f"正在获取股票 {clean_code} 的{timeframe}数据...")
        
        # 尝试连接MiniQMT
        try:
            connect_result = connect_miniqmt()
            if not connect_result:
                print("警告: MiniQMT连接失败，尝试使用本地TDX数据...")
        except Exception as e:
            print(f"警告: MiniQMT连接异常({e})，尝试使用本地TDX数据...")
        
        # 连接数据源
        fetcher = get_fetcher()
        if not fetcher:
            print("错误: 无法创建数据获取器")
            return None
        
        # 根据时间周期获取数据
        if timeframe in ["日线", "1d"]:
            df = fetcher.get_stock_daily_data(clean_code, count=250)
        elif timeframe in ["30分钟线", "30m"]:
            df = fetcher.get_stock_30min_data(clean_code, count=500)
        elif timeframe in ["周线", "1w"]:
            df = fetcher.get_stock_weekly_data(clean_code, count=100)
        else:
            print(f"错误: 不支持的时间周期 '{timeframe}'")
            return None
        
        if df is None or df.empty:
            print(f"错误: 无法获取股票 {clean_code} 的数据")
            print("提示: 请确保MiniQMT正在运行或本地TDX数据文件存在")
            return None
        
        print(f"? 成功获取 {len(df)} 条{timeframe}数据")
        return df
        
    except Exception as e:
        print(f"获取股票数据时出错: {e}")
        return None


def analyze_stock_signals(stock_code, timeframe="日线", show_details=True):
    """分析股票技术信号"""
    print(f"\n{'='*60}")
    print(f"开始分析股票 {stock_code} 的技术信号 ({timeframe})")
    print(f"{'='*60}")
    
    # 获取股票数据
    df = get_stock_data(stock_code, timeframe)
    if df is None:
        return None
    
    # 创建信号检测器
    detector = TechnicalSignalDetector()
    
    # 加载数据
    if not detector.load_data(df, timeframe):
        return None
    
    # 计算技术指标
    if not detector.calculate_indicators():
        return None
    
    # 检测所有信号
    signals = detector.detect_all_signals()
    
    # 打印信号分析结果
    detector.print_signals(show_details)
    
    return detector


def main():
    """主程序入口"""
    import sys

    print("技术信号检测与分析程序")
    print("支持的时间周期: 日线、30分钟线、周线")

    # 获取股票代码 - 支持命令行参数或交互式输入
    if len(sys.argv) >= 2:
        # 命令行参数模式
        user_input = sys.argv[1].strip()
        # 获取时间周期参数
        timeframe_choice = sys.argv[2].strip() if len(sys.argv) > 2 else '1'
    else:
        # 交互式输入模式
        print("请输入6位股票代码或指数板块代码：")
        user_input = input().strip()
        print("请选择时间周期:")
        print("1. 日线")
        print("2. 30分钟线")
        print("3. 周线")
        timeframe_choice = input("请选择(1-3，默认为1): ").strip()
        if not timeframe_choice:
            timeframe_choice = '1'

    # 验证股票代码格式
    if not re.match(r'^\d{6}$', user_input):
        print("错误: 请输入6位数字的股票代码")
        sys.exit(1)

    timeframe_map = {
        '1': '日线',
        '2': '30分钟线',
        '3': '周线'
    }

    if timeframe_choice not in timeframe_map:
        print(f"错误: 无效的时间周期选择 '{timeframe_choice}'，使用默认日线")
        timeframe_choice = '1'

    timeframe = timeframe_map[timeframe_choice]

    try:
        # 分析股票信号
        detector = analyze_stock_signals(user_input, timeframe, show_details=True)
        print(f"\n{'-'*60}")
        print("分析完成")

    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
