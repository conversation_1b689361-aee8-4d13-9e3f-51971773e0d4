# -*- coding: gbk -*-
import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# 添加项目目录到路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入ZZsignal模块
from ZZsignal import TechnicalSignalDetector

# 创建测试数据，包含可能有.000000000的时间戳
dates = [
    np.datetime64('2025-01-20T00:00:00.000000000'),
    np.datetime64('2025-01-21T00:00:00.000000000'),
    np.datetime64('2025-01-22T00:00:00.000000000'),
    np.datetime64('2025-01-23T00:00:00.000000000'),
    np.datetime64('2025-01-24T00:00:00.000000000'),
    np.datetime64('2025-01-25T00:00:00.000000000'),
    np.datetime64('2025-01-26T00:00:00.000000000'),
    np.datetime64('2025-01-27T00:00:00.000000000'),
    np.datetime64('2025-01-28T00:00:00.000000000'),
]

# 创建测试K线数据
test_data = pd.DataFrame({
    '日期': dates,
    '开盘': [100, 101, 102, 103, 104, 105, 106, 107, 108],
    '最高': [102, 103, 104, 105, 106, 107, 108, 109, 110],
    '最低': [99, 100, 101, 102, 103, 104, 105, 106, 107],
    '收盘': [101, 102, 103, 104, 105, 106, 107, 108, 109],
    '成交量': [1000000, 1100000, 1200000, 1300000, 1400000, 1500000, 1600000, 1700000, 1800000],
    '成交额': [101000000, 112200000, 123600000, 135200000, 147000000, 159000000, 171200000, 183600000, 196200000]
})

print("测试数据的时间戳格式:")
for i, date in enumerate(test_data['日期']):
    print(f"{i+1}. {date} (type: {type(date)})")

print("\n开始测试信号检测...")

# 创建信号检测器
detector = TechnicalSignalDetector()

# 加载数据
if detector.load_data(test_data, "日线"):
    print("数据加载成功")
    
    # 计算指标
    if detector.calculate_indicators():
        print("指标计算成功")
        
        # 检测信号
        signals = detector.detect_all_signals()
        
        print(f"\n检测到 {len(signals)} 个信号:")
        for i, signal in enumerate(signals, 1):
            print(f"{i}. {signal}")
    else:
        print("指标计算失败")
else:
    print("数据加载失败")
